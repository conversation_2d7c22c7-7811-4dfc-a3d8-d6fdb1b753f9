import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'

import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-vue-components/vite'
import Components from 'unplugin-vue-components/vite'
import { ArcoResolver, TDesignResolver } from 'unplugin-vue-components/resolvers'
import { codeInspectorPlugin } from 'code-inspector-plugin';
// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 6001,
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    UnoCSS(),
    // vueDevTools(),
    AutoImport({
      resolvers: [ArcoResolver(),
        TDesignResolver({
        library: 'vue-next'
      })],
    }),
    Components({
      resolvers: [
        ArcoResolver({
          sideEffect: true
        }),
        TDesignResolver({
          library: 'vue-next',
        })
      ]
    }),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
})
