/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    ADsubmenu: typeof import('@arco-design/web-vue')['Dsubmenu']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputGroup: typeof import('@arco-design/web-vue')['InputGroup']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APageHeader: typeof import('@arco-design/web-vue')['PageHeader']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASplit: typeof import('@arco-design/web-vue')['Split']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    CopyUrlFormatSelect: typeof import('./src/components/business/CopyUrlFormatSelect.vue')['default']
    FileIconPreview: typeof import('./src/components/FilePreview/FileIconPreview.vue')['default']
    FilePreview: typeof import('./src/components/FilePreview/FilePreview.vue')['default']
    FileUpload: typeof import('./src/components/FileUpload/FileUpload.vue')['default']
    FileUploadImageItem: typeof import('./src/components/FileItem/FileUploadImageItem/FileUploadImageItem.vue')['default']
    FileUploadingItem: typeof import('./src/components/FileItem/FileUploadingItem/FileUploadingItem.vue')['default']
    FileUploadLineItem: typeof import('./src/components/FileItem/FileUploadLineItem/FileUploadLineItem.vue')['default']
    GuideUse: typeof import('./src/components/GuideUse/GuideUse.vue')['default']
    InputButton: typeof import('./src/components/plugin/Button/InputButton.vue')['default']
    InputFileFormat: typeof import('./src/components/plugin/InputFormat/InputFileFormat.vue')['default']
    InputFormat: typeof import('./src/components/plugin/InputFormat/InputFormat.vue')['default']
    LazyImg: typeof import('./src/components/FilePreview/preview/LazyImg.vue')['default']
    LazyLoader: typeof import('./src/components/common/container/LazyLoader/LazyLoader.vue')['default']
    LeftMenu: typeof import('./src/components/common/LeftMenu/LeftMenu.vue')['default']
    LeftMenuSplitPanel: typeof import('./src/components/common/container/LeftMenuSplitPanel/LeftMenuSplitPanel.vue')['default']
    RadioGroup: typeof import('./src/components/plugin/RadioGroup/RadioGroup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectSearch: typeof import('./src/components/plugin/select/SelectSearch.vue')['default']
    SettingDivision: typeof import('./src/components/common/menu/SettingDivision/SettingDivision.vue')['default']
    SettingGroup: typeof import('./src/components/common/menu/SettingGroup/SettingGroup.vue')['default']
    SettingItem: typeof import('./src/components/common/menu/SettingItem/SettingItem.vue')['default']
    TBadge: typeof import('tdesign-vue-next')['Badge']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCascader: typeof import('tdesign-vue-next')['Cascader']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TCollapse: typeof import('tdesign-vue-next')['Collapse']
    TCollapsePanel: typeof import('tdesign-vue-next')['CollapsePanel']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDoption: typeof import('tdesign-vue-next')['Doption']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TDropdownItem: typeof import('tdesign-vue-next')['DropdownItem']
    TDropdownMenu: typeof import('tdesign-vue-next')['DropdownMenu']
    TEmpty: typeof import('tdesign-vue-next')['Empty']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    TIco: typeof import('tdesign-vue-next')['Ico']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputGroup: typeof import('tdesign-vue-next')['InputGroup']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLink: typeof import('tdesign-vue-next')['Link']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPopconfirm: typeof import('tdesign-vue-next')['Popconfirm']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadio: typeof import('tdesign-vue-next')['Radio']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTabPanel: typeof import('tdesign-vue-next')['TabPanel']
    TTabs: typeof import('tdesign-vue-next')['Tabs']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTooltip: typeof import('tdesign-vue-next')['Tooltip']
    UContextMenu: typeof import('./src/components/common/contextMenu/UContextMenu.vue')['default']
    WheelWaveContainer: typeof import('./src/components/common/container/WheelWaveContainer/WheelWaveContainer.vue')['default']
  }
}
