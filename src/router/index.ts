import { createRouter, createWebHashHistory } from 'vue-router'
import type { MenuRouterItem } from '@xiaou66/u-web-ui'

// 路由列表
// @unocss-include
export const routes: MenuRouterItem[] = [
  {
    path: '/uploadFileHome',
    name: 'uploadFileHome',
    component: () => import('@/views/UploadFileHome/uploadFileHome.vue'),
    meta: {
      menu: true,
      title: '上传',
      icon: 'i-u-upload-one',
    }
  },
  {
    path: '/storageSource',
    name: 'StorageSource',
    component: () => import('@/views/StorageSource/storageSource.vue'),
    props: route => ({ ...route.query }),
    meta: {
      menu: true,
      title: '存储源',
      icon: 'i-u-cloud-storage',
    }
  },
  {
    path: '/uploadScene',
    name: 'uploadScene',
    component: () => import('@/views/UploadScene/uploadScene.vue'),
    meta: {
      menu: true,
      title: '上传场景',
      icon: 'i-u-application-effect',
    }
  },
  {
    path: '/fileBox',
    name: 'fileBox',
    component: () => import('@/views/FileBox/fileBox.vue'),
    meta: {
      menu: true,
      title: '文件盒子',
      icon: 'i-u-receive',
    }
  },
  {
    path: '/userSetting',
    name: 'UserSetting',
    component: () => import('@/views/UserSetting/userSetting.vue'),
    meta: {
      menu: true,
      title: '设置',
      icon: 'i-u-setting-one',
    }
  },
  {
    path: '/dataSetting',
    name: 'DataSetting',
    component: () => import('@/views/DataSetting/dataSetting.vue'),
    meta: {
      menu: true,
      title: '数据管理',
      icon: 'i-u-data-all',
    }
  },
  {
    path: '/developerTool',
    name: 'DeveloperTool',
    component: () => import('@/views/PluginManager/pluginManager.vue'),
    meta: {
      menu: true,
      title: '插件管理',
      icon: 'i-u-plugin',
    }
  }
];


const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
})

export default router
