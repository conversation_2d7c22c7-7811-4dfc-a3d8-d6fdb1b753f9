@import "@xiaou66/u-web-ui/dist/u-web-ui.css";
@import '../base.css';

@import 'arco-ui';
@import "./theme/index";

body {
  overflow: hidden;
  //background: var(--utools-background);
}
.u-main-content, .u-main-content-no-padding {
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
}
.u-main-content {
  padding: 10px;
}
.no-flex .arco-form-item-label-col-flex {
  flex: none !important;
}
.waterfall-list {
  background: transparent !important;
}
.arco-message {
  border-radius: 10px !important;
  border: none !important;
}
.u-h-100 {
  height: 100%;
}
.u-w-100 {
  width: 100%;
}

.arco-dropdown {
  padding: 0 !important;
  line-height: 32px !important;
}

.arco-collapse {
  border: none !important;
  line-height: normal !important;
  .arco-collapse-item-header {
    background: transparent !important;
    line-height: 12px;
    font-size: 12px;
  }
  .arco-collapse-item-header-title {
    font-weight: 700 !important;
  }
  .arco-collapse-item-active > .arco-collapse-item-header {
    border-color: transparent;
  }
  .arco-icon-hover.arco-collapse-item-icon-hover:hover::before {
    background: transparent !important;
  }
  .arco-collapse-item {
    border-bottom: none;

  }
  .arco-collapse-item-content {
    background: transparent;
    padding-left: 14px;
  }
  .arco-collapse-item-header-left {
    padding-left: 32px;
    transform: translateX(-7px);
  }
}

.u-transparent {
  &.arco-btn-secondary,
  &.arco-btn-secondary[type='button'],
  &.arco-btn-secondary[type='submit'] {
    background-color: transparent;
  }
  &.arco-btn {
    background-color: transparent;
  }
  &.arco-picker {
    background-color: transparent;
  }
  .arco-select {
    background: transparent;
  }
  &.arco-input-wrapper {
    background: transparent !important;
    &:hover {
      border: 1px solid var(--color-neutral-3);
    }
    &:focus-within {
      border: 1px solid rgb(var(--arcoblue-5));
    }
  }
  &.arco-select-view {
    background: transparent;
  }
}
