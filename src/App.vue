<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import { useRoute, useRouter } from 'vue-router'
import { h, onMounted, ref } from 'vue'
import { Modal } from '@arco-design/web-vue'
import { useStorageSourceStore, useUserSettingStore } from '@/stores'
import { UtoolsLayout } from '@xiaou66/u-web-ui'
import { routes } from '@/router'

const route = useRoute();
const router = useRouter();
useEventListener(document, 'dragenter', (e) => {
  if (route.name !== 'UploadFileHome') {
    router.replace({ name: 'UploadFileHome' });
  }
});

const newUser = ref(!utools.dbStorage.getItem('new'));
const storageSourceStore = useStorageSourceStore()
const userSettingStore = useUserSettingStore();

function guideUse() {
  Modal.confirm({
    title: '插件初始化选择',
    escToClose: false,
    maskClosable: false,
    content: () => h('div', {class: ''}, [
      h('div', {}, '「创建默认上传源」将会自动下载「16 图床」存储源插件并创建存储源'),
      h('div', {}, '「我要自己探索」 将不会做任何的初始化'),
      h('div', { style: {color: '#f15', paddingTop: '10px'} },
        ' 默认图床仅提供测试不保证图片不会丢失'),
    ]),
    okText: '创建默认上传源',
    onBeforeOk: async () => {
      const pluginCode = '16image';
      if (!window.storagePlugInManager.isInstall(pluginCode)) {
        await window.storagePlugInManager.install(pluginCode);
      }

      const id = storageSourceStore.saveStorageSource({
        id: '',
        storageName: '16图床',
        storagePluginCode: pluginCode,
      });
      const pluginConfig = await window.storagePlugInManager.getPluginConfig(pluginCode);
      if (pluginConfig && pluginConfig.noConfig) {
        const key = `storageSource/${id}`;
        const config: Record<string, any> = {};
        const pluginInstance = window.storagePlugInManager.getPluginInstance(pluginCode);
        pluginInstance.verifyConfig(config);
        utools.dbCryptoStorage.setItem(key, config);
      }

      userSettingStore.currentSelectUploadId = id;
    },
    cancelText: '我要自己探索',
    onClose() {
      utools.dbStorage.setItem('new', true);
      newUser.value = false;
    },
  })
}

onMounted(() => {
  newUser.value && guideUse()
});


const useUserCount = ref(0)
onMounted(async () => {
  useUserCount.value = await window.plugInUtils.getPlugUserCount()
});
function handlePlugInHome() {
  utools.shellOpenExternal('utools://图床 Plus')
}

const appName = import.meta.env.VITE_NAME;
const avatar = utools.getUser()?.avatar || '/logo.png';
function handleLoadRouter() {
  return routes;
}
</script>

<template>
  <UtoolsLayout :title="appName"
                :avatar="avatar"
                :load-router="handleLoadRouter"/>
<!--  <div class="u-fx">-->
<!--    <LeftMenu />-->
<!--    <div id="content-wrapper">-->
<!--      &lt;!&ndash; 全局 header &ndash;&gt;-->
<!--      <div id="global-header">-->
<!--        <div></div>-->
<!--        <div  v-if="useUserCount"-->
<!--              id="use-user-count"-->
<!--              class="u-fx u-fac u-pointer"-->
<!--              @click="handlePlugInHome">-->
<!--          <iconpark-icon name="fire" style="padding-right: 4px;"></iconpark-icon>-->
<!--          你正在与-->
<!--          <a-tooltip>-->
<!--            <template #content>-->
<!--              <span style="font-size: 12px">数据来自 uTools 插件统计提供</span>-->
<!--            </template>-->
<!--            <span>{{useUserCount}}</span>-->
<!--          </a-tooltip>-->
<!--          位小伙伴一起使用</div>-->
<!--      </div>-->
<!--      <div v-if="!newUser" id="content">-->
<!--        <router-view />-->
<!--      </div>-->
<!--      <div v-if="!newUser" id="global-footer">-->
<!--        <div class="u-fx">-->
<!--          <div> © {{new Date().getFullYear()}} [xiaou]。保留所有权利</div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->

</template>

<style lang="less" scoped>

#content-wrapper {
  width: 100%;
  min-height: 100vh;
  padding: 10px 4px;
  display: grid;
  grid-template-rows: 15px calc(100vh - 26px - 26px) 15px;
  background: var(--utools-background);
  #global-header {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px 0 20px;
    position: relative;
    top: -6px;
    #use-user-count {
      font-size: 12px;
      span {
        padding: 0 2px;
        font-weight: 700;
      }
    }
  }
  #content {
    box-sizing: border-box;
    background: var(--main-background-transparent);
    border-radius: 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
    max-height: 100%;
  }
  #global-footer {
    position: relative;
    bottom: -5px;
    >div {
      justify-content: center;
      font-size: 12px;
    }
  }
}
</style>
