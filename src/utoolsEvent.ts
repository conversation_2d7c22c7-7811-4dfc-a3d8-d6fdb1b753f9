import { addUtoolsCodeEventListener } from '@xiaou66/u-utools'
import { useUploadFileListStore } from '@/stores'
import type { FileUploadItem } from '@/@types'
import { Notification } from "@arco-design/web-vue";


addUtoolsCodeEventListener('upload.file', (e) => {
  if (!e.pluginEnterParams.payload || !e.pluginEnterParams.payload.length) {
    return;
  }
  const uploadFileListStore = useUploadFileListStore();
  const filePaths = e.pluginEnterParams.payload as any[];
  const fileItems = filePaths.filter(item => item.isFile)
    .map(item => {
      return {
        dataType: 'filePath',
        content: item.path,
      } as FileUploadItem;
    });
  uploadFileListStore.addUploadFileItemByDefault(...fileItems);
});

addUtoolsCodeEventListener('upload.clipboard', (e) => {
  const clipboardImage = window.clipboard.readImage('clipboard');
  let base64 = null;
  if (!clipboardImage.isEmpty()) {
    const fileUrl = window.clipboard.read('public.file-url');
    console.log('fileUrl',  fileUrl)

    if (fileUrl) {
      base64 = window.nativeImage.createFromPath(fileUrl.replace('file://', '')).toDataURL();
    } else {
      base64 = clipboardImage.toDataURL()
    }
  } else if (utools.isWindows()) {
    const filePath = window.clipboard.readBuffer('FileNameW')
      .toString('ucs2')
      .replace(RegExp(String.fromCharCode(0), 'g'), '');
    const nativeImage = window.nativeImage.createFromPath(filePath);
    if (!nativeImage.isEmpty()) {
      base64 = nativeImage.toDataURL();
    }
  }

  if (base64) {
    const uploadFileListStore = useUploadFileListStore();
    uploadFileListStore.addUploadFileItemByDefault({
      dataType: 'base64',
      content: base64,
    } as FileUploadItem);
  } else {
    Notification.warning({
      title: '读取剪贴板',
      content: `未读取到图片无法自动上传`,
    })
  }
});


addUtoolsCodeEventListener('upload.base64', (e) => {
  if (!e.pluginEnterParams.payload || !e.pluginEnterParams.payload.length) {
    return;
  }
  const uploadFileListStore = useUploadFileListStore();
  uploadFileListStore.addUploadFileItemByDefault({
    dataType: 'base64',
    content: e.pluginEnterParams.payload,
  } as FileUploadItem);
});
