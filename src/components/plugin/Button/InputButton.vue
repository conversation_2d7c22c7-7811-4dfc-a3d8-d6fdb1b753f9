<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'
import { Notification } from '@arco-design/web-vue'

const props = defineProps<{
  formItem: StorageUIFormElement
}>();

const model = defineModel<any>('model-value');

async function handleClick() {
  if (props.formItem.formItem) {
    try {
      await props.formItem.formItem.click(model)
    }catch (e: any) {
      Notification.error({
        title: `${props.formItem.formItem.label} 失败`,
        content: e.message,
      });
    }
  }
}
</script>

<template>
  <t-input-group style="width: 100%">
    <t-input readonly v-model:model-value="model[formItem.formItem.field]" />
    <t-button v-bind="formItem.elementProperty"
              @click="handleClick">
      {{ formItem.formItem.buttonText || '获取' }}
    </t-button>
  </t-input-group>
</template>

<style scoped lang="less">

</style>
