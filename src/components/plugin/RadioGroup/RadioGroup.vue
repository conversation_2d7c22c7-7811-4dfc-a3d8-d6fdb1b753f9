<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'

 defineProps<{
  formItem: StorageUIFormElement
}>();

</script>
<template>
  <t-radio-group variant="default-filled"
                 :defaultValue="formItem.formItem.defaultValue">
    <template v-for="item in formItem.formItem.data"
              :key="item.value">
      <t-tooltip v-if="item.tooltip">
        <template #content>
          {{ item.tooltip }}
        </template>
        <t-radio-button :value="item.value">
          {{ item.label }}
        </t-radio-button>
      </t-tooltip>
      <t-radio-button v-else
               :value="item.value">
        {{ item.label }}
      </t-radio-button>
    </template>
  </t-radio-group>
</template>
<style lang="less" scoped>

</style>
