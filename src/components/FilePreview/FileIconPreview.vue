<script setup lang="ts">
import FilePreview from './FilePreview.vue';
import { computed } from 'vue'
import FileConstants from '@/constant/FileConstants.ts'

const props = defineProps<{
  content: string;
  fileSuffix: string;
}>();

const isImage = computed<boolean>(() => {
  return FileConstants.IMAGE_TYPES.includes(props.fileSuffix.toLowerCase());
});

</script>

<template>
  <div class="file-preview">
    <a-trigger v-if="isImage"
               trigger="hover"
               position="right">
      <template #content>
        <div style="background: var(--main-background); width: 40vw;">
          <FilePreview :content="content"
                       :file-suffix="fileSuffix" />
        </div>
      </template>
      <FilePreview :content="content"
                   :file-suffix="fileSuffix" />
    </a-trigger>
    <div v-else class="u-pos-rel">
      <div class="i-u-file" />
<!--      <iconpark-icon size="64" name="file"></iconpark-icon>-->
    </div>
  </div>
</template>

<style scoped lang="less">
// 文件预览
.file-preview {
  width: 64px;
  height: 64px;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
