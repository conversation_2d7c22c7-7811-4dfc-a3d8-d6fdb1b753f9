<script lang="ts" setup>
import { onMounted, ref, toRefs } from 'vue'
import type { StoragePlugInConfig } from '@xiaou66/picture-plugin'
import { usePluginStore } from '@/stores/PluginStore/PluginStore.ts'
import type { PlugInfo } from '@xiaou66/picture-plugin'
import { useUserSettingStore } from '@/stores'
import { Scrollbar, PageHeader } from '@xiaou66/u-web-ui';
import { MessagePlugin } from 'tdesign-vue-next'

const pluginStore = usePluginStore()
const { pluginAutoUpdate } = toRefs(useUserSettingStore())

function loadLocalPlugIn() {
  const dirPathList = utools.showOpenDialog({
    title: '打开文件',
    properties: ['openDirectory'],
  })
  if (!dirPathList || dirPathList.length == 0) {
    return
  }
  const dirPath = dirPathList[0]
  window.storagePlugInManager.loadPlugin(dirPath)
  pluginStore.loadLocalManualPlugin(dirPath)
  loadAllPluginConfig()
}

const allPluginConfig = ref<StoragePlugInConfig[]>([])

function loadAllPluginConfig() {
 window.storagePlugInManager.getAllPluginConfig()
    .then(res => {
      allPluginConfig.value = res;
    });
}

const updatePluginCodeList = ref<string[]>([])

async function refreshUpdatePluginList() {
  updatePluginCodeList.value = await window.storagePlugInManager.getAllUpdatePluginList()
}

function handleUpdatePlugin(pluginInfo: PlugInfo) {
  window.storagePlugInManager.install(pluginInfo.pluginCode).then(() => {
    MessagePlugin.success(`「${pluginInfo.pluginName}」更新完成`)
    refreshUpdatePluginList()
    loadAllPluginConfig()
  })
}

onMounted(() => {
  loadAllPluginConfig()
  refreshUpdatePluginList()
})

// 卸载插件物理
function handleUninstallPlugin(plugInConfig: StoragePlugInConfig) {
  window.storagePlugInManager.uninstallPlugin(plugInConfig.pluginInfo.pluginCode);
  loadAllPluginConfig();
  MessagePlugin.success('卸载成功');
}

const dev = utools.isDev()
</script>
<template>
  <PageHeader size="small" title="插件列表"
              subtitle="这里可以查看所有已安装的插件">
    <template #extra>
      <t-tooltip content="启动插件自动检查更新" position="top">
        <t-checkbox v-model:value="pluginAutoUpdate" class="text-sm"> 自动更新 </t-checkbox>
      </t-tooltip>
      <t-button v-if="dev"
                theme="default"
                size="small"
                @click="() => loadLocalPlugIn()">
        加载本地插件
      </t-button>
    </template>
  </PageHeader>
  <Scrollbar style="max-height: calc(100vh - 105px)">
    <div v-if="allPluginConfig.length" class="p-1">
      <div v-for="item in allPluginConfig"
           :key="item.pluginInfo.pluginCode"
           class="plugin-item">
        <div class="plugin-container">
          <div class="plugin-info">
            <div class="plugin-logo">
              <img :src="item.pluginInfo.pluginLogo" alt="" />
            </div>
            <div class="plugin-details">
              <div class="plugin-name">{{ item.pluginInfo.pluginName }}</div>
              <div class="plugin-desc">{{ item.pluginInfo.pluginDesc }}</div>
            </div>
          </div>
          <div class="plugin-version">
            <t-tag variant="outline" theme="success">
              {{item.pluginInfo.pluginVersion}}
            </t-tag>
          </div>
          <div class="u-fx u-gap10">
            <t-popconfirm
              placement="left"
              type="info"
              content="确认要卸载吗?"
              confirm-btn="卸载"
              @confirm="() => handleUninstallPlugin(item)"
            >
              <t-button size="small"
                        theme="danger"
                        variant="outline"
                        class="u-web-transparent uninstall-btn">
                卸载
              </t-button>
            </t-popconfirm>
            <t-button
              v-if="updatePluginCodeList.includes(item.pluginInfo.pluginCode)"
              size="small"
              theme="default"
              @click="() => handleUpdatePlugin(item.pluginInfo)"
            >
              更新
            </t-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <t-empty style="padding-top: 100px;"
               title="无安装的插件" />
    </div>
  </Scrollbar>
</template>
<style lang="less" scoped>
.plugin-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.plugin-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--main-background);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.plugin-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.plugin-logo {
  img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
  }
}

.plugin-details {
  width: 360px;
  .plugin-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .plugin-desc {
    font-size: 12px;
    color: var(--text-tips-color);
  }
}

.plugin-version {
  margin: 0 24px;
}

.uninstall-btn {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}
</style>
