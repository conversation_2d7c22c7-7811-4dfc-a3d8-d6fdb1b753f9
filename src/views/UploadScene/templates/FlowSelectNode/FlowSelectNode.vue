<script setup lang="ts">
import { UContextMenu, type UContextMenuInstance } from '@/components/common';
import { ref, useTemplateRef, nextTick } from 'vue';
import { FLOW_NODE_GROUP, type IFlowNode, SELECT_GROUP_SORT_LIST } from '@/views/UploadScene/flow'
import { useFlowInject } from '../../flow/node/BaseFlowNode.ts'

const contextMenuRef = useTemplateRef<UContextMenuInstance>('contextMenuRef');

const nodeList = defineModel<IFlowNode[]>('nodeList', {
  default: () => [],
});

function handleOpenContextMenu(e: MouseEvent) {
  contextMenuRef.value?.show(e);
}

const { createNode } = useFlowInject();
</script>

<template>
  <u-context-menu ref="contextMenuRef">
    <div class="u-fx u-fc u-fac u-pointer flow-select-node"
         @click="handleOpenContextMenu">
      <div class="i-u-plus w-6 h-6"></div>
    </div>
    <template #content>
      <div class="min-dropdown-select-options">
        <a-dsubmenu
          v-for="groupKey in SELECT_GROUP_SORT_LIST"
          :key="groupKey"
          trigger="hover"
        >
          <template #default>{{ groupKey }}</template>
          <template #content>
            <div
              v-for="flowNode in FLOW_NODE_GROUP[groupKey]!"
              :key="flowNode.info.code"
              class="min-dropdown-select min-dropdown-select-options"
              @click="createNode(flowNode)"
            >
              <a-doption>{{ flowNode.info.title }}</a-doption>
            </div>
          </template>
        </a-dsubmenu>
      </div>
    </template>
  </u-context-menu>
</template>

<style scoped lang="less">
.flow-select-node {
  width: 100%;
  height: 28px;
  border: 2px dashed #ccc;
}
</style>
