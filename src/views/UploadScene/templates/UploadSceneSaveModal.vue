<script setup lang="ts">
import {nextTick, ref, useTemplateRef} from 'vue'
import type { UploadSceneSaveModalInstance } from './UploadSceneSaveModal.ts'
import { type SceneInfoItem, useSceneStore } from '@/stores'
import { cloneDeep } from 'es-toolkit'
import type { FormInstanceFunctions } from 'tdesign-vue-next'

const visible = ref(false);
const defaultFormValue: SceneInfoItem  = {
  id: '',
  sceneName: '',
  enable: false,
};
const form = ref(cloneDeep(defaultFormValue));
function show(sceneInfo?: SceneInfoItem) {
  form.value = cloneDeep(defaultFormValue);
  if (sceneInfo) {
    form.value = {
      ...form.value,
      ...sceneInfo,
    }
  }
  visible.value = true;
  nextTick(() => {
    sceneNameInputRef.value?.focus()
  })
}
defineExpose<UploadSceneSaveModalInstance>({
  show,
});
const formRef = useTemplateRef<FormInstanceFunctions>('formRef')
async function handleVerifyForm() {
  const res = await formRef.value?.validate();
  console.log('res', res)
  debugger
  if (res !== true) {
    return false;
  }
  const id = sceneStore.saveSceneInfo(form.value)
  emits('saveOk', id);
  visible.value = false;
}
const sceneStore = useSceneStore();

const emits = defineEmits<{
  saveOk: [id: string];
}>()

const sceneNameInputRef = useTemplateRef<HTMLInputElement>('sceneNameInputRef');



</script>

<template>
  <t-dialog v-model:visible="visible"
            header="保存上传场景"
            confirm-btn="保存"
           @confirm="handleVerifyForm">
    <t-form v-model:model="form"
            ref="formRef">
      <t-form-item
        label="场景名称"
        name="sceneName"
        style="padding: 0"
        :rules="[{ required: true, message: '请取一个好听场景名称' }]">
        <t-input ref="sceneNameInputRef"
                 v-model:model-value="form.sceneName"
                 @pressEnter="handleVerifyForm" />
      </t-form-item>
      <t-form-item style="display: none"
                   :status-icon="false" />
    </t-form>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
