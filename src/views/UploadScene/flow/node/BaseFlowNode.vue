<script setup lang="ts">
import type { IFlowNodeInfo } from './BaseFlowNode.ts';
import  { useFlowInject } from './BaseFlowNode.ts';
defineProps<IFlowNodeInfo & {id: string, disableDelete?: boolean}>();
const flowProvide = useFlowInject();
</script>

<template>
  <div class="flow">
    <t-collapse-panel :key="id">
      <template #header>
<!--        <div class="flex items-center justify-between">-->
<!--          <div class="u-fx u-fac u-gap5">-->
<!--            <div>{{ title }}</div>-->
<!--            <div v-if="desc" class="u-tips">-->
<!--              {{ desc }}-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="u-fx u-fac action">-->
<!--            111-->
<!--            <div class="u-tips">{{ id }}</div>-->
<!--            111-->
<!--            <t-button v-if="!disableDelete"-->
<!--                      shape="round"-->
<!--                      size="small"-->
<!--                      @click.stop="flowProvide.removeNode(id)">-->
<!--              <template #icon>-->
<!--                <t-icon class="i-u-delete" color="rgb(var(&#45;&#45;red-5))"></t-icon>-->
<!--              </template>-->
<!--            </t-button>-->
<!--          </div>-->
<!--        </div>-->
      </template>
      <slot name="default"></slot>
    </t-collapse-panel>
  </div>
</template>

<style scoped lang="less">
:deep(.arco-collapse-item-header-left) {
  padding-right: 0;
}
body[arco-theme="dark"] {
  .flow {
    &:hover {
      //background: #3E3E3E;
    }
  }
}
.flow {
  padding: 2px;
  border: 1.5px solid transparent;
  border-radius: 10px;
  transition: all 200ms linear;
  .action {
    transition: all 320ms linear;
    opacity: 0;
  }

  &:hover {
    //background-color: #ffffff;
    border: 1.5px solid var(--color-neutral-3);
    .action {
      opacity: 1;
    }
  }
}
</style>
