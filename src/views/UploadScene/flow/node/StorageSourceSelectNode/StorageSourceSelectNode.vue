<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue'
import { CONFIG, type IStorageSourceSelectData } from './StorageSourceSelectNode.ts'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { useStorageSourceStore } from '@/stores'

defineProps<{
  node: IFlowNode<IStorageSourceSelectData>;
}>();

const storageSourceStore = useStorageSourceStore();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <div class="u-fx u-fac u-gap5">
      <t-select v-model:value="node.nodeData!.storageSourceIds"
                size="small"
                filterable
                clearable
                multiple>
        <template #label>存储源</template>
        <t-option
          v-for="item in storageSourceStore.storageSourceList"
          :key="item.id"
          :value="item.id"
          :label="item.storageName"
        />
      </t-select>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
