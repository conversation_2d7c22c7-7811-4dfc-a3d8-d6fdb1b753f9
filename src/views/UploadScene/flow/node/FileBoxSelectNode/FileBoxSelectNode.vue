<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue'
import { CONFIG, type IFileBoxSelectData } from './FileBoxSelectNode.ts'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { useFileBoxStore } from '@/stores'
defineProps<{
  node: IFlowNode<IFileBoxSelectData>;
}>();

const fileBoxStore = useFileBoxStore()
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <div class="u-fx u-fac u-gap5 u-mb10">
      <t-select v-model:model-value="node!.nodeData.fileBoxId"
                size="small"
                filterable
                clearable>
        <template #label>盒子</template>
        <t-option
          v-for="item in fileBoxStore.fileBoxInfoList"
          :key="item.id"
          :value="item.id"
          :label="item.fileBoxName"
        />
      </t-select>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">
</style>
