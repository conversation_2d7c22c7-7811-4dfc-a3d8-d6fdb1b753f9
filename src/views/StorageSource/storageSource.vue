<script setup lang="ts">
import { LeftMenuSplitPanel } from '@/components/common'
import UploadSourceSaveDrawer from './templates/StorageSourceSaveDrawer.vue';
import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import StorageSourceDetail from './templates/StorageSourceDetail.vue'
import type {
  StorageSourceSaveDrawerInstance,
} from './templates/StorageSourceSaveDrawer.ts'
import { Modal } from '@arco-design/web-vue'
import { SplitPanel } from '@xiaou66/u-web-ui';


const props = defineProps<{
  mode?: 'add'
}>();

const storageSourceRef = useTemplateRef<StorageSourceSaveDrawerInstance>('storageSourceRef');

const storageSourceStore = useStorageSourceStore();

const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}

function handleDeleteStorageSource(storageSource: StorageSourceItem) {
  Modal.confirm({
    escToClose: false,
    closable: false,
    maskClosable: false,
    title: '二次确认',
    content: `是否删除「${storageSource.storageName}」存储源, 删除后无法还原`,
    onBeforeOk: async () => {
      storageSourceStore.deleteStorageSource(storageSource.id);
      return true;
    }
  });
}
onMounted(() => {
  if (storageSourceStore.storageSourceList.length > 0) {
    activeId.value = storageSourceStore.storageSourceList[0].id
  }
});

watch(() => props.mode, () => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
onMounted(() => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
</script>

<template>
  <div class="u-main-content-no-padding">
    <UploadSourceSaveDrawer ref="storageSourceRef"
                            @saveOk="(id) => setActiveId(id)">
    </UploadSourceSaveDrawer>
    <SplitPanel default-size="160"
                min="160">
      <template #first>
        <div class="container">
          <div class="u-fx u-fac u-f-between u-mb10">
            <div class="u-font-size-smail"
                 style="padding-left: 8px;">上传源</div>
            <div>
              <t-button size="small"
                        theme="default"
                        class="u-web-transparent"
                        @click="() => storageSourceRef!.show()">
                <template #icon>
                  <t-icon class="i-u-plus"></t-icon>
                </template>
                添加
              </t-button>
            </div>
          </div>
          <div class="menu-sub">
            <div
              v-for="item in storageSourceStore.storageSourceList"
              :key="item.id"
              class="u-fx u-fac u-gap10 menu-item"
              style="justify-content: space-between"
              :class="{ active: activeId === item.id}"
              @click="() => setActiveId(item.id)">
              <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                <div>{{ item.storageName }}</div>
              </div>
              <div>
                <t-dropdown size="small" placement="bottom">
                  <div class="i-u-more-one"></div>
                  <t-dropdown-menu>
                    <t-dropdown-item @click="() => storageSourceRef?.show(item)">
                      <template #prefixIcon>
                        <div class="i-u-write"></div>
                      </template>
                      编辑
                    </t-dropdown-item>
                    <t-dropdown-item theme="error"
                                     @click="() => handleDeleteStorageSource(item)">
                      <template #prefixIcon>
                        <div class="i-u-delete"></div>
                      </template>
                      删除
                    </t-dropdown-item>
                  </t-dropdown-menu>
                </t-dropdown>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #second>
        <StorageSourceDetail v-if="activeId"
                             :storage-id="activeId"
                             :storageSourceRef="storageSourceRef!">
        </StorageSourceDetail>
      </template>
    </SplitPanel>
  </div>
</template>

<style scoped lang="less">
.container {
  box-sizing: border-box;
  padding: 4px;
  height: 100%;
  background: var(--left-menu-background-color);
}
// 手势菜单
.menu-sub {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .menu-item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    iconpark-icon {
      font-size: 16px;
    }
    &:hover {
      background-color: var(--select-hover);
    }

    // 选中状态
    &.active {
      background-color: var(--select-hover);
    }
  }
}
</style>
