<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { type FileLibraryItem, useFileBoxStore } from '@/stores'
import { FileUploadLineItem } from '@/components/FileItem'
import { useRouter } from 'vue-router'
import type { ScrollbarInstance } from '@arco-design/web-vue'

const props = defineProps<{
  fileBoxId: string;
}>();

const queryParams = ref<{
  page: number;
  pageSize: number;
}>({
  page: 1,
  pageSize: 10,
})
const listData = ref<{
  total: number;
  list: FileLibraryItem[],
}>({
  total: 0,
  list: [],
});

const fileBoxStore = useFileBoxStore();

function requestPage(page = 1, autoTop = true) {
  listData.value = fileBoxStore.getFileLibraryList(props.fileBoxId, page, queryParams.value.pageSize);
  if (page !== 1 && listData.value.list.length === 0) {
    queryParams.value.page -= 1;
    requestPage(queryParams.value.page);
  }
  if (autoTop && scrollbarRef.value) {
    scrollbarRef.value.scrollTop(0);
  }
}
watch(() => props.fileBoxId, () => {
  requestPage();
});
onMounted(() => {
  requestPage();
});

const scrollbarRef = useTemplateRef<ScrollbarInstance>('scrollbarRef');

const router = useRouter();
function toUploadFilePage() {
  router.replace({ name: 'UploadFileHome' });
}
function handleAfterDelete() {
  setTimeout(() => {
    requestPage(queryParams.value.page, false)
  });
}
</script>

<template>
  <div class="u-main-content-no-padding" style="padding: 5px 0 5px 5px">
    <a-scrollbar v-if="listData.total > 0"
                 ref="scrollbarRef"
                 outer-class="scrollbar-outer"
                 style="overflow: auto; max-height: 100%">
      <div ref="listRef">
        <div v-for="fileLibrary in listData.list"
             :key="fileLibrary.id">
          <FileUploadLineItem :file-library="fileLibrary"
                              @afterDelete="handleAfterDelete" />
        </div>
      </div>
    </a-scrollbar>
    <div v-if="listData.total === 0" style="padding: 60px">
      <t-empty />
    </div>
    <div v-if="listData.total > 0" class="u-fx u-f-between" style="padding-right: 6px;">
      <div></div>
      <div>
        <a-pagination show-total
                      size="small"
                      v-model:page-size="queryParams.pageSize"
                      v-model:current="queryParams.page"
                      :total="listData.total"
                      @change="requestPage" simple />
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
:default(.arco-pagination-item) {
  margin: 0;
}
.scrollbar-outer {
  height: calc(100% - 30px);
}
</style>
