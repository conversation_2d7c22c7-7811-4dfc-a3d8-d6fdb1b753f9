<script lang="ts" setup>
import { FileUpload } from '@/components/FileUpload'
import type { FileUpdateInfoItem, FileUploadItem } from '@/@types'
import { computed, toRefs, useTemplateRef } from 'vue'
import { useEventListener } from '@vueuse/core'
import FileUtils from '@/utils/FileUtils.ts';
import { useStorageSourceStore, useUploadFileListStore, useUserSettingStore, useSceneStore } from '@/stores'
import { Message, type ScrollbarInstance } from '@arco-design/web-vue'
import type { CascaderNode } from '@arco-design/web-vue/es/cascader/interface'
import { FileUploadImageItem, FileUploadingItem, FileUploadLineItem } from '@/components/FileItem'
import { LazyLoader } from '@/components/common'
import FileIconPreview from '@/components/FilePreview/FileIconPreview.vue'
import { LazyImg, Waterfall } from 'vue-waterfall-plugin-next'
import { CopyUrlFormatSelect } from '@/components/business'

const storageSourceStore = useStorageSourceStore();
const { currentSelectUploadId, uploadPageViewMode } = toRefs(useUserSettingStore());
const uploadFileListStore = useUploadFileListStore();
function fileAdd(fileList: FileUploadItem[]) {
  if (!currentSelectUploadId.value) {
    Message.warning("选择上传方式");
  }
  console.log('fileAdded', fileList);
  uploadFileListStore.addUploadFileItemByDefault(...fileList);
}

const mainContentRef = useTemplateRef<HTMLDivElement>('mainContentRef');
const scrollbarRef = useTemplateRef<ScrollbarInstance>('scrollbarRef');

// const router = useRouter();
// function toRouterAddStoreSource() {
//   router.replace({
//     name: 'StorageSource',
//     query: {
//       mode: 'add',
//     }
//   });
// }

// 当用户拖拽文件进入自动回到顶部
useEventListener(mainContentRef, 'dragenter', (e) => {
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTop(0);
  }
});
const sceneStore = useSceneStore();

const selectUploadSourceOptions = computed<CascaderNode[]>(() => {
  const storageSourceList = storageSourceStore.storageSourceList;
  const sceneInfoList = sceneStore.sceneInfoList;
  return [
    {
      label: '上传场景',
      value: 'uploadScene',
      children: sceneInfoList
        .map(item => ({ label: item.sceneName, value: item.id})),
    },
    {
      label: '存储源',
      value: 'storageSource',
      children: storageSourceList
        .filter(item => utools.dbCryptoStorage.getItem(`storageSource/${item.id}`))
        .map(item => ({label: item.storageName, value: item.id})),
    }
  ] as CascaderNode[];
});

// 重新上传
function handleReloadUpload(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.reloadUploadFileItem(uploadFileItem)
}
function handleRemoveUploadItem(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.removeUploadItem(uploadFileItem);
  Message.success("移除成功");
}
</script>

<template>
  <div class="u-main-content-no-padding"
       style="overflow:hidden"
       ref="mainContentRef">
    <a-scrollbar ref="scrollbarRef" outer-class="u-h-100"
                 style="overflow: auto;
                      height: 100%;">
      <div>
        <FileUpload @add="fileAdd"></FileUpload>
        <div class="u-fx u-f-between upload-controller">
          <div class="u-fx u-gap5">
            <t-cascader :options="selectUploadSourceOptions"
                        v-model:value="currentSelectUploadId"
                        :style="{ width:'220px' }"
                        placeholder="选择上传方式"
                        size="small"
                        filterable>
            </t-cascader>
            <t-tooltip  content="自动复制上传文件后地址格式, 批量上传不建议使用">
              <CopyUrlFormatSelect />
            </t-tooltip>
          </div>
          <div class="u-fx u-fac u-gap10">
            <t-tooltip  position="left">
              <template #content>
                <span class="u-font-size-smail">超过100个会将最早上传的移除</span>
              </template>
              <div class="u-font-size-smail u-pointer">
                当前上传记录最大值为 100 个
              </div>
            </t-tooltip>
            <t-radio-group v-model:value="uploadPageViewMode"
                           size="small"
                           variant="default-filled">
              <t-tooltip content="列表">
                <t-radio-button value="list">
                  <div class="i-u-view-grid-list" />
                </t-radio-button>
              </t-tooltip>
              <t-tooltip content="大图">
                <t-radio-button value="image">
                  <div class="i-u-picture" />
                </t-radio-button>
              </t-tooltip>
            </t-radio-group>
          </div>
        </div>
        <div v-if="uploadPageViewMode === 'list'" class="upload-file-container">
          <!--   待上传   -->
          <div v-for="(fileItem) in uploadFileListStore.uploadFileList"
               :key="fileItem.id">
            <FileUploadingItem :data="fileItem"
                               :select-upload-source-options="selectUploadSourceOptions"></FileUploadingItem>
          </div>
          <!--   历史上传   -->
          <div v-for="(fileLibrary) in uploadFileListStore.uploadFileHistoryList"
               :key="fileLibrary.id">
            <div style="min-height: 50px">
              <div>
                <LazyLoader h="68px">
                  <FileUploadLineItem :file-library="fileLibrary" />
                </LazyLoader>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="uploadPageViewMode === 'image'"
             class="upload-file-container">
          <!--   待上传   -->
          <div v-for="(fileItem) in uploadFileListStore.uploadFileList"
               :key="fileItem.id"
               class="u-fx u-f-between u-fac upload-file-item">
            <div class="file-info">
              <div class="file-preview">
                <FileIconPreview :content="fileItem.filePath ? `file://${fileItem.filePath}` : fileItem.content!"
                                 :file-suffix="fileItem.fileSuffix" />
              </div>
              <div class="file-details">
                <div class="file-name">{{ fileItem.fileName }}</div>
                <div class="file-size" v-if="fileItem.fileSize">
                  {{ FileUtils.formatFileSize(fileItem.fileSize) }}
                </div>
              </div>
            </div>
            <div>
              <a-tag v-if="fileItem.uploadStatus === 'waiting'"
                     style="border-radius: 10px">等待上传</a-tag>
              <a-tag v-else-if="fileItem.uploadStatus === 'uploading'"
                     style="border-radius: 10px"
                     color="green"
                     loading>
                上传中
              </a-tag>
              <a-tag v-else-if="fileItem.uploadStatus === 'failed'"
                     style="border-radius: 10px"
                     color="red">
                上传失败
              </a-tag>
            </div>
            <div class="u-fx u-gap5" style="flex-direction: column; align-items: end;">
              <a-cascader v-if="fileItem.uploadStatus === 'failed'"
                          :options="selectUploadSourceOptions"
                          v-model:model-value="fileItem.uploadWay"
                          :style="{width:'150px'}"
                          placeholder="选择上传方式"
                          size="mini"
                          allow-search>
              </a-cascader>
              <div v-if="fileItem.uploadStatus === 'failed'"
                   class="u-font-size-smail u-fx u-gap10">
                <a-link style="font-size: 12px" status="danger"
                        @click="() => handleRemoveUploadItem(fileItem)">移除</a-link>
                <a-link style="font-size: 12px"
                        @click="() => handleReloadUpload(fileItem)">
                  重新上传
                </a-link>
              </div>
            </div>
          </div>
          <!--   历史上传   -->
          <Waterfall :list="uploadFileListStore.uploadFileHistoryList">
            <template #item="{ item: fileLibrary }">
              <div class="card">
                <FileUploadImageItem :fileLibrary="fileLibrary" />
              </div>
            </template>
          </Waterfall>
        </div>
      </div>
    </a-scrollbar>

  </div>
</template>

<style lang="less" scoped>
:deep(.arco-radio-button-content) {
  padding: 0px 8px;
}
.upload-controller {
  padding: 0 10px;
}

.upload-file-container {
  padding: 10px;
}
.upload-file-item {
  padding: 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--text-color);

  &:hover {
    background: var(--u-hover-color);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
