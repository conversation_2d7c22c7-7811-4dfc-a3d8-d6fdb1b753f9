<script lang="ts" setup>
import { ref, toRefs } from 'vue'
import { useUserSettingStore } from '@/stores'
import { InputFormat } from '../../components/plugin/InputFormat'
import UrlFormatSetting from '@/views/UserSetting/templates/UrlFormatSetting.vue'
import {
  PageHeader,
  SettingGroup,
  SettingItem,
  SettingDivision,
  SwitchPlusEnable,
  Scrollbar,
} from '@xiaou66/u-web-ui'

const serviceStatus = ref(window.linkService.status)

const { serviceEnableStatus, pluginTempFilename, uploadTaskConcurrency } =
  toRefs(useUserSettingStore())

async function handleServiceEnableStatusChange(val: boolean) {
  serviceEnableStatus.value = val
  if (val) {
    await window.linkService.start()
  } else {
    await window.linkService.stop()
  }
  serviceStatus.value = window.linkService.status
}
</script>
<template>
  <PageHeader size="small"
              title="用户设置"
              subtitle="这里可以设置一些个性化和开启本地服务的地方" />
  <Scrollbar style="height: calc(100vh - 115px); overflow-y: auto">
    <div class="group">
      <SettingGroup title="基础配置">
        <SettingItem
          title="服务开关"
          :click="true"
          @click="handleServiceEnableStatusChange(!serviceEnableStatus)"
        >
          <template #desc>
            <div class="u-fx u-fac u-gap10">
              <div class="u-font-size-smail" style="color: #c3c3c3">服务状态</div>
              <div>
                <a-tag size="small" v-if="serviceStatus" color="green">运行中</a-tag>
                <a-tag size="small" v-else color="red">停止</a-tag>
              </div>
            </div>
          </template>
          <SwitchPlusEnable
            v-model:value="serviceEnableStatus"
            style="width: 46px"
            @click.stop
            @change="(value: any) => handleServiceEnableStatusChange(value as any)"
          >
          </SwitchPlusEnable>
        </SettingItem>
        <SettingDivision />
        <SettingItem
          title="同时上传的任务数"
          desc="仅对添加到队列上传任务生效, 建议自建源可以调整第三方源不要进行调整"
        >
          <t-tooltip content="配置后需要完全重启插件" position="left">
            <t-input-number
              v-model:model-value="uploadTaskConcurrency"
              :min="1"
              :max="5"
              style="width: 100px"
              size="small"
              theme="column"
            />
          </t-tooltip>
        </SettingItem>
        <SettingItem
          title="内存文件命名"
          desc="使用 base64 上传的文件因为没有文件名称需要命名一个文件名"
        >
          <div style="width: 330px">
            <InputFormat v-model:model-value="pluginTempFilename" />
          </div>
        </SettingItem>
      </SettingGroup>
      <SettingGroup title="链接配置">
        <SettingItem>
          <UrlFormatSetting></UrlFormatSetting>
        </SettingItem>
      </SettingGroup>
    </div>
  </Scrollbar>
</template>
<style lang="less" scoped>
.group {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px 16px;
}
</style>
