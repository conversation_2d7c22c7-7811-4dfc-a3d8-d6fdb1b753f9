{"name": "picture-bed-pro", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite --mode dev", "dev:prod": "vite --mode prod  --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@vueuse/core": "^13.6.0", "@xiaou66/interconnect-service": "^0.0.16", "@xiaou66/picture-plugin": "workspace:^", "@xiaou66/u-utools": "^0.0.3", "@xiaou66/u-web-ui": "0.0.45", "async": "^3.2.6", "dayjs": "^1.11.13", "es-toolkit": "^1.39.8", "nanoid": "^5.1.5", "ofetch": "^1.4.1", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-waterfall-plugin-next": "^2.6.8"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@iconify/utils": "^3.0.0", "@prettier/plugin-oxc": "^0.0.4", "@tsconfig/node22": "^22.0.2", "@types/async": "^3.2.24", "@types/jsdom": "^21.1.7", "@types/node": "^22.16.5", "@unocss/preset-icons": "^66.4.1", "@unocss/transformer-directives": "^66.4.1", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "code-inspector-plugin": "^1.0.4", "electron": "22.3.27", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "less": "^4.4.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "prettier": "3.6.2", "tdesign-vue-next": "^1.15.2", "typescript": "~5.8.0", "unocss": "^66.4.1", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "utools-api-types": "^7.2.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}